/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"fmt"

	baseCommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	platformFiles "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/files/common"
	platformRequest "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/request/common"
)

// DownloadService implements IDownloadServiceInterface
type DownloadService struct {
	requestService platformRequest.IRequestServiceInterface
	fileService    platformFiles.IFileService
}

// ServiceBrand implements BrandedService interface
func (ds *DownloadService) ServiceBrand() interface{} {
	return nil
}

// NewDownloadService creates a new download service instance
func NewDownloadService(
	requestService platformRequest.IRequestServiceInterface,
	fileService platformFiles.IFileService,
) *DownloadService {
	return &DownloadService{
		requestService: requestService,
		fileService:    fileService,
	}
}

// Download downloads a resource from URI to target URI
func (ds *DownloadService) Download(resource *baseCommon.URI, target *baseCommon.URI, cancellationToken baseCommon.CancellationToken) error {
	// Handle file and vscode-remote schemes by copying directly
	if resource.Scheme == baseCommon.SchemaFile || resource.Scheme == baseCommon.SchemaVscodeRemote {
		// Intentionally only support this for file|remote<->file|remote scenarios
		_, err := ds.fileService.Copy(resource, target, true)
		return err
	}

	// For other schemes, use HTTP request
	options := &platformRequest.IRequestOptions{
		Type: "GET",
		URL:  resource.ToString(true),
	}

	context, err := ds.requestService.Request(options, cancellationToken)
	if err != nil {
		return err
	}
	defer context.Stream.Close()

	if context.Res.StatusCode == 200 {
		// Create a reader from the response stream
		reader := context.Stream

		// Write the stream to the target file
		_, err := ds.fileService.WriteFile(target, reader, nil)
		return err
	} else {
		// Get error message from response
		message, err := platformRequest.AsTextOrError(context)
		if err != nil {
			message = err.Error()
		}
		return fmt.Errorf("Expected 200, got back %d instead.\n\n%s", context.Res.StatusCode, message)
	}
}
