/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"fmt"
	"strings"
	"sync"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	"github.com/yudaprama/kawai-agent/vscode_go/vs/nls"
	dialogscommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/dialogs/common"
	notificationcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/notification/common"
)

const DEBUG = false

// getResourceLabel returns a label for the resource
func getResourceLabel(resource *basecommon.URI) string {
	if resource.Scheme == basecommon.SchemaFile {
		return resource.FSPath()
	}
	return resource.Path
}

var stackElementCounter int
var stackElementCounterMutex sync.Mutex

// getNextStackElementID returns the next stack element ID
func getNextStackElementID() int {
	stackElementCounterMutex.Lock()
	defer stackElementCounterMutex.Unlock()
	stackElementCounter++
	return stackElementCounter
}

// ResourceStackElement represents a resource-scoped stack element
type ResourceStackElement struct {
	ID                int                 `json:"id"`
	Type              UndoRedoElementType `json:"type"`
	Actual            IUndoRedoElement    `json:"actual"`
	Label             string              `json:"label"`
	ConfirmBeforeUndo bool                `json:"confirmBeforeUndo"`
	ResourceLabel     string              `json:"resourceLabel"`
	StrResource       string              `json:"strResource"`
	ResourceLabels    []string            `json:"resourceLabels"`
	StrResources      []string            `json:"strResources"`
	GroupID           int                 `json:"groupId"`
	GroupOrder        int                 `json:"groupOrder"`
	SourceID          int                 `json:"sourceId"`
	SourceOrder       int                 `json:"sourceOrder"`
	IsValid           bool                `json:"isValid"`
}

// NewResourceStackElement creates a new ResourceStackElement
func NewResourceStackElement(actual IUndoRedoElement, resourceLabel, strResource string, groupID, groupOrder, sourceID, sourceOrder int) *ResourceStackElement {
	return &ResourceStackElement{
		ID:                getNextStackElementID(),
		Type:              UndoRedoElementTypeResource,
		Actual:            actual,
		Label:             actual.GetLabel(),
		ConfirmBeforeUndo: actual.GetConfirmBeforeUndo(),
		ResourceLabel:     resourceLabel,
		StrResource:       strResource,
		ResourceLabels:    []string{resourceLabel},
		StrResources:      []string{strResource},
		GroupID:           groupID,
		GroupOrder:        groupOrder,
		SourceID:          sourceID,
		SourceOrder:       sourceOrder,
		IsValid:           true,
	}
}

// SetValid sets the validity of the element
func (r *ResourceStackElement) SetValid(isValid bool) {
	r.IsValid = isValid
}

// String returns a string representation of the element
func (r *ResourceStackElement) String() string {
	validStr := "  VALID"
	if !r.IsValid {
		validStr = "INVALID"
	}
	return fmt.Sprintf("[id:%d] [group:%d] [%s] %T - %v", r.ID, r.GroupID, validStr, r.Actual, r.Actual)
}

// RemovedResourceReason represents the reason a resource was removed
type RemovedResourceReason int

const (
	RemovedResourceReasonExternalRemoval RemovedResourceReason = iota
	RemovedResourceReasonNoParallelUniverses
)

// ResourceReasonPair represents a resource and the reason it was removed
type ResourceReasonPair struct {
	ResourceLabel string                `json:"resourceLabel"`
	Reason        RemovedResourceReason `json:"reason"`
}

// NewResourceReasonPair creates a new ResourceReasonPair
func NewResourceReasonPair(resourceLabel string, reason RemovedResourceReason) *ResourceReasonPair {
	return &ResourceReasonPair{
		ResourceLabel: resourceLabel,
		Reason:        reason,
	}
}

// RemovedResources manages removed resources
type RemovedResources struct {
	elements map[string]*ResourceReasonPair
	mutex    sync.RWMutex
}

// NewRemovedResources creates a new RemovedResources
func NewRemovedResources() *RemovedResources {
	return &RemovedResources{
		elements: make(map[string]*ResourceReasonPair),
	}
}

// CreateMessage creates a message describing the removed resources
func (r *RemovedResources) CreateMessage() string {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	var externalRemoval []string
	var noParallelUniverses []string

	for _, element := range r.elements {
		if element.Reason == RemovedResourceReasonExternalRemoval {
			externalRemoval = append(externalRemoval, element.ResourceLabel)
		} else {
			noParallelUniverses = append(noParallelUniverses, element.ResourceLabel)
		}
	}

	var messages []string
	if len(externalRemoval) > 0 {
		messages = append(messages, nls.Localize(
			&nls.LocalizeInfo{Key: "externalRemoval", Comment: []string{"{0} is a list of filenames"}},
			"The following files have been closed and modified on disk: {0}.",
			strings.Join(externalRemoval, ", "),
		))
	}
	if len(noParallelUniverses) > 0 {
		messages = append(messages, nls.Localize(
			&nls.LocalizeInfo{Key: "noParallelUniverses", Comment: []string{"{0} is a list of filenames"}},
			"The following files have been modified in an incompatible way: {0}.",
			strings.Join(noParallelUniverses, ", "),
		))
	}
	return strings.Join(messages, "\n")
}

// Size returns the number of removed resources
func (r *RemovedResources) Size() int {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	return len(r.elements)
}

// Has checks if a resource is in the removed resources
func (r *RemovedResources) Has(strResource string) bool {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	_, exists := r.elements[strResource]
	return exists
}

// Set adds a resource to the removed resources
func (r *RemovedResources) Set(strResource string, value *ResourceReasonPair) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	r.elements[strResource] = value
}

// Delete removes a resource from the removed resources
func (r *RemovedResources) Delete(strResource string) bool {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	_, exists := r.elements[strResource]
	if exists {
		delete(r.elements, strResource)
	}
	return exists
}

// WorkspaceStackElement represents a workspace-scoped stack element
type WorkspaceStackElement struct {
	ID                   int                       `json:"id"`
	Type                 UndoRedoElementType       `json:"type"`
	Actual               IWorkspaceUndoRedoElement `json:"actual"`
	Label                string                    `json:"label"`
	ConfirmBeforeUndo    bool                      `json:"confirmBeforeUndo"`
	ResourceLabels       []string                  `json:"resourceLabels"`
	StrResources         []string                  `json:"strResources"`
	GroupID              int                       `json:"groupId"`
	GroupOrder           int                       `json:"groupOrder"`
	SourceID             int                       `json:"sourceId"`
	SourceOrder          int                       `json:"sourceOrder"`
	RemovedResources     *RemovedResources         `json:"removedResources"`
	InvalidatedResources *RemovedResources         `json:"invalidatedResources"`
}

// NewWorkspaceStackElement creates a new WorkspaceStackElement
func NewWorkspaceStackElement(actual IWorkspaceUndoRedoElement, resourceLabels, strResources []string, groupID, groupOrder, sourceID, sourceOrder int) *WorkspaceStackElement {
	return &WorkspaceStackElement{
		ID:                   getNextStackElementID(),
		Type:                 UndoRedoElementTypeWorkspace,
		Actual:               actual,
		Label:                actual.GetLabel(),
		ConfirmBeforeUndo:    actual.GetConfirmBeforeUndo(),
		ResourceLabels:       resourceLabels,
		StrResources:         strResources,
		GroupID:              groupID,
		GroupOrder:           groupOrder,
		SourceID:             sourceID,
		SourceOrder:          sourceOrder,
		RemovedResources:     nil,
		InvalidatedResources: nil,
	}
}

// CanSplit checks if the element can be split
func (w *WorkspaceStackElement) CanSplit() bool {
	// Check if the actual element has a Split method
	_, err := w.Actual.Split()
	return err == nil
}

// RemoveResource removes a resource from the element
func (w *WorkspaceStackElement) RemoveResource(resourceLabel, strResource string, reason RemovedResourceReason) {
	if w.RemovedResources == nil {
		w.RemovedResources = NewRemovedResources()
	}
	if !w.RemovedResources.Has(strResource) {
		w.RemovedResources.Set(strResource, NewResourceReasonPair(resourceLabel, reason))
	}
}

// SetValid sets the validity of a resource in the element
func (w *WorkspaceStackElement) SetValid(resourceLabel, strResource string, isValid bool) {
	if isValid {
		if w.InvalidatedResources != nil {
			w.InvalidatedResources.Delete(strResource)
			if w.InvalidatedResources.Size() == 0 {
				w.InvalidatedResources = nil
			}
		}
	} else {
		if w.InvalidatedResources == nil {
			w.InvalidatedResources = NewRemovedResources()
		}
		if !w.InvalidatedResources.Has(strResource) {
			w.InvalidatedResources.Set(strResource, NewResourceReasonPair(resourceLabel, RemovedResourceReasonExternalRemoval))
		}
	}
}

// String returns a string representation of the element
func (w *WorkspaceStackElement) String() string {
	validStr := "  VALID"
	if w.InvalidatedResources != nil {
		validStr = "INVALID"
	}
	return fmt.Sprintf("[id:%d] [group:%d] [%s] %T - %v", w.ID, w.GroupID, validStr, w.Actual, w.Actual)
}

// StackElement represents either a resource or workspace stack element
type StackElement interface {
	String() string
}

// Ensure our types implement StackElement
var _ StackElement = (*ResourceStackElement)(nil)
var _ StackElement = (*WorkspaceStackElement)(nil)

// ResourceEditStack manages the undo/redo stack for a specific resource
type ResourceEditStack struct {
	ResourceLabel string
	strResource   string
	past          []StackElement
	future        []StackElement
	Locked        bool
	VersionID     int
	mutex         sync.RWMutex
}

// NewResourceEditStack creates a new ResourceEditStack
func NewResourceEditStack(resourceLabel, strResource string) *ResourceEditStack {
	return &ResourceEditStack{
		ResourceLabel: resourceLabel,
		strResource:   strResource,
		past:          make([]StackElement, 0),
		future:        make([]StackElement, 0),
		Locked:        false,
		VersionID:     1,
	}
}

// Dispose disposes the edit stack
func (r *ResourceEditStack) Dispose() {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	for _, element := range r.past {
		if ws, ok := element.(*WorkspaceStackElement); ok {
			ws.RemoveResource(r.ResourceLabel, r.strResource, RemovedResourceReasonExternalRemoval)
		}
	}
	for _, element := range r.future {
		if ws, ok := element.(*WorkspaceStackElement); ok {
			ws.RemoveResource(r.ResourceLabel, r.strResource, RemovedResourceReasonExternalRemoval)
		}
	}
	r.VersionID++
}

// String returns a string representation of the edit stack
func (r *ResourceEditStack) String() string {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	var result []string
	result = append(result, fmt.Sprintf("* %s:", r.strResource))

	for i := 0; i < len(r.past); i++ {
		result = append(result, fmt.Sprintf("   * [UNDO] %s", r.past[i].String()))
	}
	for i := len(r.future) - 1; i >= 0; i-- {
		result = append(result, fmt.Sprintf("   * [REDO] %s", r.future[i].String()))
	}
	return strings.Join(result, "\n")
}

// FlushAllElements removes all elements from the stack
func (r *ResourceEditStack) FlushAllElements() {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	r.past = make([]StackElement, 0)
	r.future = make([]StackElement, 0)
	r.VersionID++
}

// SetElementsIsValid sets the validity of all elements
func (r *ResourceEditStack) SetElementsIsValid(isValid bool) {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	for _, element := range r.past {
		r.setElementValidFlag(element, isValid)
	}
	for _, element := range r.future {
		r.setElementValidFlag(element, isValid)
	}
}

// setElementValidFlag sets the validity flag for an element
func (r *ResourceEditStack) setElementValidFlag(element StackElement, isValid bool) {
	if ws, ok := element.(*WorkspaceStackElement); ok {
		ws.SetValid(r.ResourceLabel, r.strResource, isValid)
	} else if rs, ok := element.(*ResourceStackElement); ok {
		rs.SetValid(isValid)
	}
}

// SetElementsValidFlag sets the validity flag for elements matching a filter
func (r *ResourceEditStack) SetElementsValidFlag(isValid bool, filter func(element IUndoRedoElement) bool) {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	for _, element := range r.past {
		var actual IUndoRedoElement
		if ws, ok := element.(*WorkspaceStackElement); ok {
			actual = ws.Actual
		} else if rs, ok := element.(*ResourceStackElement); ok {
			actual = rs.Actual
		}
		if actual != nil && filter(actual) {
			r.setElementValidFlag(element, isValid)
		}
	}
	for _, element := range r.future {
		var actual IUndoRedoElement
		if ws, ok := element.(*WorkspaceStackElement); ok {
			actual = ws.Actual
		} else if rs, ok := element.(*ResourceStackElement); ok {
			actual = rs.Actual
		}
		if actual != nil && filter(actual) {
			r.setElementValidFlag(element, isValid)
		}
	}
}

// PushElement adds an element to the stack
func (r *ResourceEditStack) PushElement(element StackElement) {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	// Remove the future
	for _, futureElement := range r.future {
		if ws, ok := futureElement.(*WorkspaceStackElement); ok {
			ws.RemoveResource(r.ResourceLabel, r.strResource, RemovedResourceReasonNoParallelUniverses)
		}
	}
	r.future = make([]StackElement, 0)
	r.past = append(r.past, element)
	r.VersionID++
}

// CreateSnapshot creates a snapshot of the current stack
func (r *ResourceEditStack) CreateSnapshot(resource *basecommon.URI) *ResourceEditStackSnapshot {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	var elements []int
	for i := 0; i < len(r.past); i++ {
		if rs, ok := r.past[i].(*ResourceStackElement); ok {
			elements = append(elements, rs.ID)
		} else if ws, ok := r.past[i].(*WorkspaceStackElement); ok {
			elements = append(elements, ws.ID)
		}
	}
	for i := len(r.future) - 1; i >= 0; i-- {
		if rs, ok := r.future[i].(*ResourceStackElement); ok {
			elements = append(elements, rs.ID)
		} else if ws, ok := r.future[i].(*WorkspaceStackElement); ok {
			elements = append(elements, ws.ID)
		}
	}

	return NewResourceEditStackSnapshot(resource, elements)
}

// RestoreSnapshot restores the stack to a previous snapshot
func (r *ResourceEditStack) RestoreSnapshot(snapshot *ResourceEditStackSnapshot) {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	snapshotLength := len(snapshot.Elements)
	isOK := true
	snapshotIndex := 0
	removePastAfter := -1

	for i := 0; i < len(r.past); i++ {
		element := r.past[i]
		var elementID int
		if rs, ok := element.(*ResourceStackElement); ok {
			elementID = rs.ID
		} else if ws, ok := element.(*WorkspaceStackElement); ok {
			elementID = ws.ID
		}

		if isOK && (snapshotIndex >= snapshotLength || elementID != snapshot.Elements[snapshotIndex]) {
			isOK = false
			removePastAfter = 0
		}
		if !isOK {
			if ws, ok := element.(*WorkspaceStackElement); ok {
				ws.RemoveResource(r.ResourceLabel, r.strResource, RemovedResourceReasonExternalRemoval)
			}
		}
		snapshotIndex++
	}

	removeFutureBefore := -1
	for i := len(r.future) - 1; i >= 0; i-- {
		element := r.future[i]
		var elementID int
		if rs, ok := element.(*ResourceStackElement); ok {
			elementID = rs.ID
		} else if ws, ok := element.(*WorkspaceStackElement); ok {
			elementID = ws.ID
		}

		if isOK && (snapshotIndex >= snapshotLength || elementID != snapshot.Elements[snapshotIndex]) {
			isOK = false
			removeFutureBefore = i
		}
		if !isOK {
			if ws, ok := element.(*WorkspaceStackElement); ok {
				ws.RemoveResource(r.ResourceLabel, r.strResource, RemovedResourceReasonExternalRemoval)
			}
		}
		snapshotIndex++
	}

	if removePastAfter != -1 {
		r.past = r.past[:removePastAfter]
	}
	if removeFutureBefore != -1 {
		r.future = r.future[removeFutureBefore+1:]
	}
	r.VersionID++
}

// GetElements returns the past and future elements
func (r *ResourceEditStack) GetElements() *IPastFutureElements {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	var past []IUndoRedoElement
	var future []IUndoRedoElement

	for _, element := range r.past {
		if ws, ok := element.(*WorkspaceStackElement); ok {
			past = append(past, ws.Actual)
		} else if rs, ok := element.(*ResourceStackElement); ok {
			past = append(past, rs.Actual)
		}
	}
	for _, element := range r.future {
		if ws, ok := element.(*WorkspaceStackElement); ok {
			future = append(future, ws.Actual)
		} else if rs, ok := element.(*ResourceStackElement); ok {
			future = append(future, rs.Actual)
		}
	}

	return &IPastFutureElements{Past: past, Future: future}
}

// GetClosestPastElement returns the most recent past element
func (r *ResourceEditStack) GetClosestPastElement() StackElement {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	if len(r.past) == 0 {
		return nil
	}
	return r.past[len(r.past)-1]
}

// GetSecondClosestPastElement returns the second most recent past element
func (r *ResourceEditStack) GetSecondClosestPastElement() StackElement {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	if len(r.past) < 2 {
		return nil
	}
	return r.past[len(r.past)-2]
}

// GetClosestFutureElement returns the most recent future element
func (r *ResourceEditStack) GetClosestFutureElement() StackElement {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	if len(r.future) == 0 {
		return nil
	}
	return r.future[len(r.future)-1]
}

// HasPastElements checks if there are past elements
func (r *ResourceEditStack) HasPastElements() bool {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	return len(r.past) > 0
}

// HasFutureElements checks if there are future elements
func (r *ResourceEditStack) HasFutureElements() bool {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	return len(r.future) > 0
}

// SplitPastWorkspaceElement splits a past workspace element
func (r *ResourceEditStack) SplitPastWorkspaceElement(toRemove *WorkspaceStackElement, individualMap map[string]*ResourceStackElement) {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	for j := len(r.past) - 1; j >= 0; j-- {
		if r.past[j] == toRemove {
			if replacement, exists := individualMap[r.strResource]; exists {
				// gets replaced
				r.past[j] = replacement
			} else {
				// gets deleted
				r.past = append(r.past[:j], r.past[j+1:]...)
			}
			break
		}
	}
	r.VersionID++
}

// SplitFutureWorkspaceElement splits a future workspace element
func (r *ResourceEditStack) SplitFutureWorkspaceElement(toRemove *WorkspaceStackElement, individualMap map[string]*ResourceStackElement) {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	for j := len(r.future) - 1; j >= 0; j-- {
		if r.future[j] == toRemove {
			if replacement, exists := individualMap[r.strResource]; exists {
				// gets replaced
				r.future[j] = replacement
			} else {
				// gets deleted
				r.future = append(r.future[:j], r.future[j+1:]...)
			}
			break
		}
	}
	r.VersionID++
}

// MoveBackward moves an element from past to future
func (r *ResourceEditStack) MoveBackward(element StackElement) {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	if len(r.past) > 0 {
		r.past = r.past[:len(r.past)-1]
		r.future = append(r.future, element)
		r.VersionID++
	}
}

// MoveForward moves an element from future to past
func (r *ResourceEditStack) MoveForward(element StackElement) {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	if len(r.future) > 0 {
		r.future = r.future[:len(r.future)-1]
		r.past = append(r.past, element)
		r.VersionID++
	}
}

// EditStackSnapshot represents a snapshot of edit stacks
type EditStackSnapshot struct {
	EditStacks []*ResourceEditStack
	versionIDs []int
}

// NewEditStackSnapshot creates a new EditStackSnapshot
func NewEditStackSnapshot(editStacks []*ResourceEditStack) *EditStackSnapshot {
	versionIDs := make([]int, len(editStacks))
	for i, editStack := range editStacks {
		versionIDs[i] = editStack.VersionID
	}
	return &EditStackSnapshot{
		EditStacks: editStacks,
		versionIDs: versionIDs,
	}
}

// IsValid checks if the snapshot is still valid
func (e *EditStackSnapshot) IsValid() bool {
	for i, editStack := range e.EditStacks {
		if e.versionIDs[i] != editStack.VersionID {
			return false
		}
	}
	return true
}

// missingEditStack is a locked edit stack used as a placeholder
var missingEditStack = func() *ResourceEditStack {
	stack := NewResourceEditStack("", "")
	stack.Locked = true
	return stack
}()

// UndoRedoService implements IUndoRedoService
type UndoRedoService struct {
	editStacks                map[string]*ResourceEditStack
	uriComparisonKeyComputers [][2]interface{} // [scheme, UriComparisonKeyComputer]
	dialogService             dialogscommon.IDialogService
	notificationService       notificationcommon.INotificationService
	mutex                     sync.RWMutex
}

// NewUndoRedoService creates a new UndoRedoService
func NewUndoRedoService(
	dialogService dialogscommon.IDialogService,
	notificationService notificationcommon.INotificationService,
) *UndoRedoService {
	return &UndoRedoService{
		editStacks:                make(map[string]*ResourceEditStack),
		uriComparisonKeyComputers: make([][2]interface{}, 0),
		dialogService:             dialogService,
		notificationService:       notificationService,
	}
}

// ServiceBrand implements the service brand
func (u *UndoRedoService) ServiceBrand() interface{} {
	return "undoRedoService"
}

// RegisterUriComparisonKeyComputer registers a URI comparison key computer
func (u *UndoRedoService) RegisterUriComparisonKeyComputer(scheme string, uriComparisonKeyComputer UriComparisonKeyComputer) basecommon.IDisposable {
	u.mutex.Lock()
	defer u.mutex.Unlock()

	u.uriComparisonKeyComputers = append(u.uriComparisonKeyComputers, [2]interface{}{scheme, uriComparisonKeyComputer})

	return basecommon.ToDisposable(func() {
		u.mutex.Lock()
		defer u.mutex.Unlock()
		for i, computer := range u.uriComparisonKeyComputers {
			if computer[1] == uriComparisonKeyComputer {
				u.uriComparisonKeyComputers = append(u.uriComparisonKeyComputers[:i], u.uriComparisonKeyComputers[i+1:]...)
				return
			}
		}
	})
}

// GetUriComparisonKey gets the comparison key for a URI
func (u *UndoRedoService) GetUriComparisonKey(resource *basecommon.URI) string {
	u.mutex.RLock()
	defer u.mutex.RUnlock()

	for _, computer := range u.uriComparisonKeyComputers {
		if computer[0] == resource.Scheme {
			if comp, ok := computer[1].(UriComparisonKeyComputer); ok {
				return comp.GetComparisonKey(resource)
			}
		}
	}
	return resource.ToString()
}

// print prints debug information
func (u *UndoRedoService) print(label string) {
	if !DEBUG {
		return
	}
	fmt.Println("------------------------------------")
	fmt.Printf("AFTER %s: \n", label)

	u.mutex.RLock()
	defer u.mutex.RUnlock()

	var str []string
	for _, editStack := range u.editStacks {
		str = append(str, editStack.String())
	}
	fmt.Println(strings.Join(str, "\n"))
}

// PushElement adds a new element to the undo stack
func (u *UndoRedoService) PushElement(element IUndoRedoElement, group *UndoRedoGroup, source *UndoRedoSource) {
	if group == nil {
		group = UndoRedoGroupNone
	}
	if source == nil {
		source = UndoRedoSourceNone
	}

	if element.GetType() == UndoRedoElementTypeResource {
		if resourceElement, ok := element.(IResourceUndoRedoElement); ok {
			resourceLabel := getResourceLabel(resourceElement.GetResource())
			strResource := u.GetUriComparisonKey(resourceElement.GetResource())
			u.pushElement(NewResourceStackElement(element, resourceLabel, strResource, group.ID, group.NextOrder(), source.ID, source.NextOrder()))
		}
	} else {
		if workspaceElement, ok := element.(IWorkspaceUndoRedoElement); ok {
			seen := make(map[string]bool)
			var resourceLabels []string
			var strResources []string

			for _, resource := range workspaceElement.GetResources() {
				resourceLabel := getResourceLabel(resource)
				strResource := u.GetUriComparisonKey(resource)

				if seen[strResource] {
					continue
				}
				seen[strResource] = true
				resourceLabels = append(resourceLabels, resourceLabel)
				strResources = append(strResources, strResource)
			}

			if len(resourceLabels) == 1 {
				u.pushElement(NewResourceStackElement(element, resourceLabels[0], strResources[0], group.ID, group.NextOrder(), source.ID, source.NextOrder()))
			} else {
				u.pushElement(NewWorkspaceStackElement(workspaceElement, resourceLabels, strResources, group.ID, group.NextOrder(), source.ID, source.NextOrder()))
			}
		}
	}

	if DEBUG {
		u.print("pushElement")
	}
}

// pushElement pushes a stack element to the appropriate edit stacks
func (u *UndoRedoService) pushElement(element StackElement) {
	var strResources []string
	var resourceLabels []string

	if rs, ok := element.(*ResourceStackElement); ok {
		strResources = rs.StrResources
		resourceLabels = rs.ResourceLabels
	} else if ws, ok := element.(*WorkspaceStackElement); ok {
		strResources = ws.StrResources
		resourceLabels = ws.ResourceLabels
	}

	u.mutex.Lock()
	defer u.mutex.Unlock()

	for i, strResource := range strResources {
		resourceLabel := resourceLabels[i]

		var editStack *ResourceEditStack
		if stack, exists := u.editStacks[strResource]; exists {
			editStack = stack
		} else {
			editStack = NewResourceEditStack(resourceLabel, strResource)
			u.editStacks[strResource] = editStack
		}

		editStack.PushElement(element)
	}
}

// GetLastElement gets the last pushed element for a resource
func (u *UndoRedoService) GetLastElement(resource *basecommon.URI) IUndoRedoElement {
	strResource := u.GetUriComparisonKey(resource)

	u.mutex.RLock()
	defer u.mutex.RUnlock()

	if editStack, exists := u.editStacks[strResource]; exists {
		if closestPastElement := editStack.GetClosestPastElement(); closestPastElement != nil {
			if rs, ok := closestPastElement.(*ResourceStackElement); ok {
				return rs.Actual
			} else if ws, ok := closestPastElement.(*WorkspaceStackElement); ok {
				return ws.Actual
			}
		}
	}
	return nil
}

// GetElements gets all the elements associated with a resource
func (u *UndoRedoService) GetElements(resource *basecommon.URI) *IPastFutureElements {
	strResource := u.GetUriComparisonKey(resource)

	u.mutex.RLock()
	defer u.mutex.RUnlock()

	if editStack, exists := u.editStacks[strResource]; exists {
		return editStack.GetElements()
	}
	return &IPastFutureElements{Past: []IUndoRedoElement{}, Future: []IUndoRedoElement{}}
}

// SetElementsValidFlag validates or invalidates stack elements associated with a resource
func (u *UndoRedoService) SetElementsValidFlag(resource *basecommon.URI, isValid bool, filter func(element IUndoRedoElement) bool) {
	strResource := u.GetUriComparisonKey(resource)

	u.mutex.RLock()
	defer u.mutex.RUnlock()

	if editStack, exists := u.editStacks[strResource]; exists {
		editStack.SetElementsValidFlag(isValid, filter)
	}
}

// RemoveElements removes elements that target resource
func (u *UndoRedoService) RemoveElements(resource interface{}) {
	var strResource string

	if uri, ok := resource.(*basecommon.URI); ok {
		strResource = u.GetUriComparisonKey(uri)
	} else if str, ok := resource.(string); ok {
		strResource = str
	} else {
		return
	}

	u.mutex.Lock()
	defer u.mutex.Unlock()

	if editStack, exists := u.editStacks[strResource]; exists {
		editStack.Dispose()
		delete(u.editStacks, strResource)
	}

	if DEBUG {
		u.print("removeElements")
	}
}

// CreateSnapshot creates a snapshot of the current elements on the undo-redo stack for a resource
func (u *UndoRedoService) CreateSnapshot(resource *basecommon.URI) *ResourceEditStackSnapshot {
	strResource := u.GetUriComparisonKey(resource)

	u.mutex.RLock()
	defer u.mutex.RUnlock()

	if editStack, exists := u.editStacks[strResource]; exists {
		return editStack.CreateSnapshot(resource)
	}
	return NewResourceEditStackSnapshot(resource, []int{})
}

// RestoreSnapshot attempts to restore a certain snapshot previously created with CreateSnapshot for a resource
func (u *UndoRedoService) RestoreSnapshot(snapshot *ResourceEditStackSnapshot) {
	strResource := u.GetUriComparisonKey(snapshot.Resource)

	u.mutex.RLock()
	defer u.mutex.RUnlock()

	if editStack, exists := u.editStacks[strResource]; exists {
		editStack.RestoreSnapshot(snapshot)
	}

	if DEBUG {
		u.print("restoreSnapshot")
	}
}

// getEditStackSnapshot gets a snapshot of edit stacks for the given resources
func (u *UndoRedoService) getEditStackSnapshot(strResources []string) *EditStackSnapshot {
	u.mutex.RLock()
	defer u.mutex.RUnlock()

	var editStacks []*ResourceEditStack
	for _, strResource := range strResources {
		if editStack, exists := u.editStacks[strResource]; exists {
			editStacks = append(editStacks, editStack)
		} else {
			editStacks = append(editStacks, missingEditStack)
		}
	}
	return NewEditStackSnapshot(editStacks)
}

// CanUndo checks if undo is possible for the given resource or source
func (u *UndoRedoService) CanUndo(resourceOrSource interface{}) bool {
	if uri, ok := resourceOrSource.(*basecommon.URI); ok {
		strResource := u.GetUriComparisonKey(uri)

		u.mutex.RLock()
		defer u.mutex.RUnlock()

		if editStack, exists := u.editStacks[strResource]; exists {
			return editStack.HasPastElements()
		}
		return false
	} else if source, ok := resourceOrSource.(*UndoRedoSource); ok {
		return u.canUndoInGroup(source)
	}
	return false
}

// canUndoInGroup checks if undo is possible for a source
func (u *UndoRedoService) canUndoInGroup(source *UndoRedoSource) bool {
	if source.ID == 0 {
		return false
	}

	u.mutex.RLock()
	defer u.mutex.RUnlock()

	// Find the most recent element with this source ID
	var candidate StackElement
	var candidateEditStack *ResourceEditStack

	for _, editStack := range u.editStacks {
		if closestPastElement := editStack.GetClosestPastElement(); closestPastElement != nil {
			var sourceID int
			if rs, ok := closestPastElement.(*ResourceStackElement); ok {
				sourceID = rs.SourceID
			} else if ws, ok := closestPastElement.(*WorkspaceStackElement); ok {
				sourceID = ws.SourceID
			}

			if sourceID == source.ID {
				if candidate == nil {
					candidate = closestPastElement
					candidateEditStack = editStack
				} else {
					// Compare source orders to find the most recent
					var candidateOrder int
					if rs, ok := candidate.(*ResourceStackElement); ok {
						candidateOrder = rs.SourceOrder
					} else if ws, ok := candidate.(*WorkspaceStackElement); ok {
						candidateOrder = ws.SourceOrder
					}

					var currentOrder int
					if rs, ok := closestPastElement.(*ResourceStackElement); ok {
						currentOrder = rs.SourceOrder
					} else if ws, ok := closestPastElement.(*WorkspaceStackElement); ok {
						currentOrder = ws.SourceOrder
					}

					if currentOrder > candidateOrder {
						candidate = closestPastElement
						candidateEditStack = editStack
					}
				}
			}
		}
	}

	return candidate != nil && candidateEditStack != nil
}

// undoInGroup performs undo for a source group
func (u *UndoRedoService) undoInGroup(source *UndoRedoSource) error {
	if source.ID == 0 {
		return fmt.Errorf("cannot undo in group with ID 0")
	}

	u.mutex.RLock()
	defer u.mutex.RUnlock()

	// Find the most recent element with this source ID
	var candidate StackElement
	var candidateEditStack *ResourceEditStack

	for _, editStack := range u.editStacks {
		if closestPastElement := editStack.GetClosestPastElement(); closestPastElement != nil {
			var sourceID int
			if rs, ok := closestPastElement.(*ResourceStackElement); ok {
				sourceID = rs.SourceID
			} else if ws, ok := closestPastElement.(*WorkspaceStackElement); ok {
				sourceID = ws.SourceID
			}

			if sourceID == source.ID {
				if candidate == nil {
					candidate = closestPastElement
					candidateEditStack = editStack
				} else {
					// Compare source orders to find the most recent
					var candidateOrder int
					if rs, ok := candidate.(*ResourceStackElement); ok {
						candidateOrder = rs.SourceOrder
					} else if ws, ok := candidate.(*WorkspaceStackElement); ok {
						candidateOrder = ws.SourceOrder
					}

					var currentOrder int
					if rs, ok := closestPastElement.(*ResourceStackElement); ok {
						currentOrder = rs.SourceOrder
					} else if ws, ok := closestPastElement.(*WorkspaceStackElement); ok {
						currentOrder = ws.SourceOrder
					}

					if currentOrder > candidateOrder {
						candidate = closestPastElement
						candidateEditStack = editStack
					}
				}
			}
		}
	}

	if candidate == nil || candidateEditStack == nil {
		return fmt.Errorf("nothing to undo for source")
	}

	if rs, ok := candidate.(*ResourceStackElement); ok {
		return u.undoResourceStackElement(rs, candidateEditStack.strResource)
	} else if ws, ok := candidate.(*WorkspaceStackElement); ok {
		return u.undoWorkspaceStackElement(ws, candidateEditStack.strResource)
	}

	return fmt.Errorf("unknown element type")
}

// Undo performs undo operation for the given resource or source
func (u *UndoRedoService) Undo(resourceOrSource interface{}) error {
	if uri, ok := resourceOrSource.(*basecommon.URI); ok {
		return u.undoResource(uri)
	} else if source, ok := resourceOrSource.(*UndoRedoSource); ok {
		return u.undoInGroup(source)
	}
	return fmt.Errorf("invalid resource or source type")
}

// undoResource performs undo for a specific resource
func (u *UndoRedoService) undoResource(resource *basecommon.URI) error {
	strResource := u.GetUriComparisonKey(resource)

	u.mutex.RLock()
	editStack, exists := u.editStacks[strResource]
	u.mutex.RUnlock()

	if !exists || !editStack.HasPastElements() {
		return fmt.Errorf("nothing to undo")
	}

	closestPastElement := editStack.GetClosestPastElement()
	if closestPastElement == nil {
		return fmt.Errorf("nothing to undo")
	}

	if rs, ok := closestPastElement.(*ResourceStackElement); ok {
		return u.undoResourceStackElement(rs, strResource)
	} else if ws, ok := closestPastElement.(*WorkspaceStackElement); ok {
		return u.undoWorkspaceStackElement(ws, strResource)
	}

	return fmt.Errorf("unknown element type")
}

// undoResourceStackElement undoes a resource stack element
func (u *UndoRedoService) undoResourceStackElement(element *ResourceStackElement, strResource string) error {
	if !element.IsValid {
		return fmt.Errorf("element is not valid")
	}

	if element.ConfirmBeforeUndo {
		message := nls.Localize(
			&nls.LocalizeInfo{Key: "confirmUndo", Comment: []string{"{0} is the operation being undone"}},
			"Would you like to undo '{0}'?",
			element.Label,
		)

		dialogType := dialogscommon.DialogTypeQuestion
		title := nls.Localize(&nls.LocalizeInfo{Key: "confirmUndoTitle"}, "Confirm Undo")
		confirmation := &dialogscommon.IConfirmation{
			IBaseDialogOptions: dialogscommon.IBaseDialogOptions{
				Type:    &dialogType,
				Title:   &title,
				Message: message,
			},
		}

		result, err := u.dialogService.Confirm(confirmation)
		if err != nil || !result.Confirmed {
			return fmt.Errorf("undo cancelled")
		}
	}

	// Perform the undo
	if err := element.Actual.Undo(); err != nil {
		u.notificationService.Error(err)
		return err
	}

	// Move the element from past to future
	u.mutex.RLock()
	editStack := u.editStacks[strResource]
	u.mutex.RUnlock()

	editStack.MoveBackward(element)

	if DEBUG {
		u.print("undo")
	}

	return nil
}

// undoWorkspaceStackElement undoes a workspace stack element
func (u *UndoRedoService) undoWorkspaceStackElement(element *WorkspaceStackElement, strResource string) error {
	if element.RemovedResources != nil && element.RemovedResources.Size() > 0 {
		u.showRemovedResourcesMessage(element.RemovedResources)
		return fmt.Errorf("cannot undo due to removed resources")
	}

	if element.InvalidatedResources != nil && element.InvalidatedResources.Size() > 0 {
		u.showInvalidatedResourcesMessage(element.InvalidatedResources)
		return fmt.Errorf("cannot undo due to invalidated resources")
	}

	if element.ConfirmBeforeUndo {
		message := nls.Localize(
			&nls.LocalizeInfo{Key: "confirmWorkspaceUndo", Comment: []string{"{0} is the operation being undone"}},
			"Would you like to undo '{0}' across all files?",
			element.Label,
		)

		dialogType := dialogscommon.DialogTypeQuestion
		title := nls.Localize(&nls.LocalizeInfo{Key: "confirmUndoTitle"}, "Confirm Undo")
		confirmation := &dialogscommon.IConfirmation{
			IBaseDialogOptions: dialogscommon.IBaseDialogOptions{
				Type:    &dialogType,
				Title:   &title,
				Message: message,
			},
		}

		result, err := u.dialogService.Confirm(confirmation)
		if err != nil || !result.Confirmed {
			return fmt.Errorf("undo cancelled")
		}
	}

	// Check if we can split the element
	if element.CanSplit() {
		return u.splitAndUndoWorkspaceElement(element)
	}

	// Prepare for undo
	var disposable basecommon.IDisposable
	if preparer, ok := element.Actual.(interface {
		PrepareUndoRedo() (basecommon.IDisposable, error)
	}); ok {
		var err error
		disposable, err = preparer.PrepareUndoRedo()
		if err != nil {
			return err
		}
		if disposable != nil {
			defer disposable.Dispose()
		}
	}

	// Perform the undo
	if err := element.Actual.Undo(); err != nil {
		u.notificationService.Error(err)
		return err
	}

	// Move the element from past to future in all affected edit stacks
	snapshot := u.getEditStackSnapshot(element.StrResources)
	for _, editStack := range snapshot.EditStacks {
		if editStack != missingEditStack {
			editStack.MoveBackward(element)
		}
	}

	if DEBUG {
		u.print("undo")
	}

	return nil
}

// splitAndUndoWorkspaceElement splits a workspace element and undoes it
func (u *UndoRedoService) splitAndUndoWorkspaceElement(element *WorkspaceStackElement) error {
	individualElements, err := element.Actual.Split()
	if err != nil {
		return err
	}

	individualMap := make(map[string]*ResourceStackElement)
	for i, individualElement := range individualElements {
		resourceLabel := element.ResourceLabels[i]
		strResource := element.StrResources[i]
		individualMap[strResource] = NewResourceStackElement(
			individualElement,
			resourceLabel,
			strResource,
			element.GroupID,
			element.GroupOrder,
			element.SourceID,
			element.SourceOrder,
		)
	}

	// Replace the workspace element with individual elements
	snapshot := u.getEditStackSnapshot(element.StrResources)
	for _, editStack := range snapshot.EditStacks {
		if editStack != missingEditStack {
			editStack.SplitPastWorkspaceElement(element, individualMap)
		}
	}

	// Now undo the individual element for the requested resource
	for strResource, individualElement := range individualMap {
		if err := u.undoResourceStackElement(individualElement, strResource); err != nil {
			return err
		}
		break // Only undo the first one (the requested resource)
	}

	return nil
}

// showRemovedResourcesMessage shows a message about removed resources
func (u *UndoRedoService) showRemovedResourcesMessage(removedResources *RemovedResources) {
	message := removedResources.CreateMessage()
	u.notificationService.Warn(message)
}

// showInvalidatedResourcesMessage shows a message about invalidated resources
func (u *UndoRedoService) showInvalidatedResourcesMessage(invalidatedResources *RemovedResources) {
	message := invalidatedResources.CreateMessage()
	u.notificationService.Warn(message)
}

// CanRedo checks if redo is possible for the given resource or source
func (u *UndoRedoService) CanRedo(resourceOrSource interface{}) bool {
	if uri, ok := resourceOrSource.(*basecommon.URI); ok {
		strResource := u.GetUriComparisonKey(uri)

		u.mutex.RLock()
		defer u.mutex.RUnlock()

		if editStack, exists := u.editStacks[strResource]; exists {
			return editStack.HasFutureElements()
		}
		return false
	} else if source, ok := resourceOrSource.(*UndoRedoSource); ok {
		return u.canRedoInGroup(source)
	}
	return false
}

// canRedoInGroup checks if redo is possible for a source
func (u *UndoRedoService) canRedoInGroup(source *UndoRedoSource) bool {
	if source.ID == 0 {
		return false
	}

	u.mutex.RLock()
	defer u.mutex.RUnlock()

	// Find the most recent element with this source ID in the future
	var candidate StackElement
	var candidateEditStack *ResourceEditStack

	for _, editStack := range u.editStacks {
		if closestFutureElement := editStack.GetClosestFutureElement(); closestFutureElement != nil {
			var sourceID int
			if rs, ok := closestFutureElement.(*ResourceStackElement); ok {
				sourceID = rs.SourceID
			} else if ws, ok := closestFutureElement.(*WorkspaceStackElement); ok {
				sourceID = ws.SourceID
			}

			if sourceID == source.ID {
				if candidate == nil {
					candidate = closestFutureElement
					candidateEditStack = editStack
				} else {
					// Compare source orders to find the most recent
					var candidateOrder int
					if rs, ok := candidate.(*ResourceStackElement); ok {
						candidateOrder = rs.SourceOrder
					} else if ws, ok := candidate.(*WorkspaceStackElement); ok {
						candidateOrder = ws.SourceOrder
					}

					var currentOrder int
					if rs, ok := closestFutureElement.(*ResourceStackElement); ok {
						currentOrder = rs.SourceOrder
					} else if ws, ok := closestFutureElement.(*WorkspaceStackElement); ok {
						currentOrder = ws.SourceOrder
					}

					if currentOrder < candidateOrder {
						candidate = closestFutureElement
						candidateEditStack = editStack
					}
				}
			}
		}
	}

	return candidate != nil && candidateEditStack != nil
}

// Redo performs redo operation for the given resource or source
func (u *UndoRedoService) Redo(resourceOrSource interface{}) error {
	if uri, ok := resourceOrSource.(*basecommon.URI); ok {
		return u.redoResource(uri)
	} else if source, ok := resourceOrSource.(*UndoRedoSource); ok {
		return u.redoInGroup(source)
	} else if str, ok := resourceOrSource.(string); ok {
		// Handle string resource
		u.mutex.RLock()
		editStack, exists := u.editStacks[str]
		u.mutex.RUnlock()

		if !exists || !editStack.HasFutureElements() {
			return fmt.Errorf("nothing to redo")
		}

		closestFutureElement := editStack.GetClosestFutureElement()
		if closestFutureElement == nil {
			return fmt.Errorf("nothing to redo")
		}

		if rs, ok := closestFutureElement.(*ResourceStackElement); ok {
			return u.redoResourceStackElement(rs, str)
		} else if ws, ok := closestFutureElement.(*WorkspaceStackElement); ok {
			return u.redoWorkspaceStackElement(ws, str)
		}
	}
	return fmt.Errorf("invalid resource or source type")
}

// redoResource performs redo for a specific resource
func (u *UndoRedoService) redoResource(resource *basecommon.URI) error {
	strResource := u.GetUriComparisonKey(resource)

	u.mutex.RLock()
	editStack, exists := u.editStacks[strResource]
	u.mutex.RUnlock()

	if !exists || !editStack.HasFutureElements() {
		return fmt.Errorf("nothing to redo")
	}

	closestFutureElement := editStack.GetClosestFutureElement()
	if closestFutureElement == nil {
		return fmt.Errorf("nothing to redo")
	}

	if rs, ok := closestFutureElement.(*ResourceStackElement); ok {
		return u.redoResourceStackElement(rs, strResource)
	} else if ws, ok := closestFutureElement.(*WorkspaceStackElement); ok {
		return u.redoWorkspaceStackElement(ws, strResource)
	}

	return fmt.Errorf("unknown element type")
}

// redoInGroup performs redo for a source group
func (u *UndoRedoService) redoInGroup(source *UndoRedoSource) error {
	if source.ID == 0 {
		return fmt.Errorf("cannot redo in group with ID 0")
	}

	u.mutex.RLock()
	defer u.mutex.RUnlock()

	// Find the most recent element with this source ID in the future
	var candidate StackElement
	var candidateEditStack *ResourceEditStack

	for _, editStack := range u.editStacks {
		if closestFutureElement := editStack.GetClosestFutureElement(); closestFutureElement != nil {
			var sourceID int
			if rs, ok := closestFutureElement.(*ResourceStackElement); ok {
				sourceID = rs.SourceID
			} else if ws, ok := closestFutureElement.(*WorkspaceStackElement); ok {
				sourceID = ws.SourceID
			}

			if sourceID == source.ID {
				if candidate == nil {
					candidate = closestFutureElement
					candidateEditStack = editStack
				} else {
					// Compare source orders to find the most recent
					var candidateOrder int
					if rs, ok := candidate.(*ResourceStackElement); ok {
						candidateOrder = rs.SourceOrder
					} else if ws, ok := candidate.(*WorkspaceStackElement); ok {
						candidateOrder = ws.SourceOrder
					}

					var currentOrder int
					if rs, ok := closestFutureElement.(*ResourceStackElement); ok {
						currentOrder = rs.SourceOrder
					} else if ws, ok := closestFutureElement.(*WorkspaceStackElement); ok {
						currentOrder = ws.SourceOrder
					}

					if currentOrder < candidateOrder {
						candidate = closestFutureElement
						candidateEditStack = editStack
					}
				}
			}
		}
	}

	if candidate == nil || candidateEditStack == nil {
		return fmt.Errorf("nothing to redo for source")
	}

	if rs, ok := candidate.(*ResourceStackElement); ok {
		return u.redoResourceStackElement(rs, candidateEditStack.strResource)
	} else if ws, ok := candidate.(*WorkspaceStackElement); ok {
		return u.redoWorkspaceStackElement(ws, candidateEditStack.strResource)
	}

	return fmt.Errorf("unknown element type")
}

// redoResourceStackElement redoes a resource stack element
func (u *UndoRedoService) redoResourceStackElement(element *ResourceStackElement, strResource string) error {
	if !element.IsValid {
		return fmt.Errorf("element is not valid")
	}

	// Perform the redo
	if err := element.Actual.Redo(); err != nil {
		u.notificationService.Error(err)
		return err
	}

	// Move the element from future to past
	u.mutex.RLock()
	editStack := u.editStacks[strResource]
	u.mutex.RUnlock()

	editStack.MoveForward(element)

	if DEBUG {
		u.print("redo")
	}

	return nil
}

// redoWorkspaceStackElement redoes a workspace stack element
func (u *UndoRedoService) redoWorkspaceStackElement(element *WorkspaceStackElement, strResource string) error {
	if element.RemovedResources != nil && element.RemovedResources.Size() > 0 {
		u.showRemovedResourcesMessage(element.RemovedResources)
		return fmt.Errorf("cannot redo due to removed resources")
	}

	if element.InvalidatedResources != nil && element.InvalidatedResources.Size() > 0 {
		u.showInvalidatedResourcesMessage(element.InvalidatedResources)
		return fmt.Errorf("cannot redo due to invalidated resources")
	}

	// Prepare for redo
	var disposable basecommon.IDisposable
	if preparer, ok := element.Actual.(interface {
		PrepareUndoRedo() (basecommon.IDisposable, error)
	}); ok {
		var err error
		disposable, err = preparer.PrepareUndoRedo()
		if err != nil {
			return err
		}
		if disposable != nil {
			defer disposable.Dispose()
		}
	}

	// Perform the redo
	if err := element.Actual.Redo(); err != nil {
		u.notificationService.Error(err)
		return err
	}

	// Move the element from future to past in all affected edit stacks
	snapshot := u.getEditStackSnapshot(element.StrResources)
	for _, editStack := range snapshot.EditStacks {
		if editStack != missingEditStack {
			editStack.MoveForward(element)
		}
	}

	if DEBUG {
		u.print("redo")
	}

	return nil
}

// NewDefaultUndoRedoService creates a new UndoRedoService with default dependencies
func NewDefaultUndoRedoService() *UndoRedoService {
	return NewUndoRedoService(
		dialogscommon.NewTestDialogService(),
		notificationcommon.NewTestNotificationService(),
	)
}
