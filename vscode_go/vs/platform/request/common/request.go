/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"context"
	"fmt"
	"io"
	"net/http"

	baseCommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	platformInstantiation "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/instantiation/common"
)

// IRequestService provides HTTP request capabilities
var IRequestService = platformInstantiation.CreateDecorator[IRequestServiceInterface]("requestService")

// AuthInfo represents authentication information
type AuthInfo struct {
	IsProxy bool   `json:"isProxy"`
	Scheme  string `json:"scheme"`
	Host    string `json:"host"`
	Port    int    `json:"port"`
	Realm   string `json:"realm"`
	Attempt int    `json:"attempt"`
}

// Credentials represents user credentials
type Credentials struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

// IRequestOptions represents options for HTTP requests
type IRequestOptions struct {
	Type    string            `json:"type"`
	URL     string            `json:"url"`
	Headers map[string]string `json:"headers,omitempty"`
	Data    interface{}       `json:"data,omitempty"`
	Timeout int               `json:"timeout,omitempty"`
}

// IRequestContext represents the context of an HTTP request
type IRequestContext struct {
	Res    *http.Response `json:"res"`
	Stream io.ReadCloser  `json:"stream"`
}

// IRequestServiceInterface defines the request service interface
type IRequestServiceInterface interface {
	platformInstantiation.BrandedService

	// Request makes an HTTP request
	Request(options *IRequestOptions, token baseCommon.CancellationToken) (*IRequestContext, error)

	// ResolveProxy resolves proxy for a URL
	ResolveProxy(url string) (string, error)

	// LookupAuthorization looks up authorization credentials
	LookupAuthorization(authInfo *AuthInfo) (*Credentials, error)

	// LookupKerberosAuthorization looks up Kerberos authorization
	LookupKerberosAuthorization(url string) (string, error)

	// LoadCertificates loads certificates
	LoadCertificates() ([]string, error)
}

// IsSuccess checks if the request was successful
func IsSuccess(context *IRequestContext) bool {
	if context.Res.StatusCode >= 200 && context.Res.StatusCode < 300 {
		return true
	}
	return context.Res.StatusCode == 1223
}

// HasNoContent checks if the response has no content
func HasNoContent(context *IRequestContext) bool {
	return context.Res.StatusCode == 204
}

// AsText converts response to text
func AsText(context *IRequestContext) (string, error) {
	if HasNoContent(context) {
		return "", nil
	}
	
	data, err := io.ReadAll(context.Stream)
	if err != nil {
		return "", err
	}
	
	return string(data), nil
}

// AsTextOrError converts response to text or returns error if not successful
func AsTextOrError(context *IRequestContext) (string, error) {
	if !IsSuccess(context) {
		return "", fmt.Errorf("Server returned %d", context.Res.StatusCode)
	}
	return AsText(context)
}
